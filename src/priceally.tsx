"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { ChevronLeft, ChevronDown, Menu, Undo2, Redo2, Paperclip, Globe, Send, Search } from "lucide-react"

export default function Component() {
  const [message, setMessage] = useState("")

  return (
    <div className="h-screen bg-[#ffffff] flex flex-col">
      {/* Header */}
      <header className="flex items-center justify-between px-6 py-3 border-b border-[#e2e2e2]">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" className="text-[#0d0d0d] hover:bg-[#fafafa]">
            <ChevronLeft className="w-4 h-4 mr-1" />
            Back
          </Button>
          <Button variant="ghost" className="text-[#0d0d0d] hover:bg-[#fafafa] font-medium">
            My new workspace
            <ChevronDown className="w-4 h-4 ml-1" />
          </Button>
        </div>

        <div className="text-xl font-semibold text-[#0d0d0d]">PriceAlly</div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            className="border-[#e2e2e2] text-[#0d0d0d] hover:bg-[#fafafa] bg-transparent"
          >
            Save workspace
          </Button>
          <Button size="sm" className="bg-[#0d0d0d] text-[#ffffff] hover:bg-[#000000]">
            Export report
          </Button>
        </div>
      </header>

      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar - Chat */}
        <div className="w-80 border-r border-[#e2e2e2] flex flex-col bg-[#fafafa]">
          {/* Chat Header */}
          <div className="flex items-center justify-between p-4 border-b border-[#e2e2e2]">
            <h2 className="font-semibold text-[#0d0d0d]">Chat</h2>
            <Button variant="ghost" size="sm">
              <Menu className="w-4 h-4" />
            </Button>
          </div>

          {/* Chat Content */}
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4 text-sm">
              <div className="text-[#0d0d0d] leading-relaxed">
                design considerations, implementation strategies, and market analysis to provide you with a complete
                overview. This will take some time to prepare, but I'll deliver a detailed and informative report.
              </div>

              <div className="space-y-3">
                <div className="font-medium text-[#0d0d0d]">What's next?</div>
                <div className="text-[#0d0d0d]">Here's what I can do for you:</div>
                <div className="space-y-2 text-[#0d0d0d]">
                  <div>
                    1. Continue developing the comprehensive report on customised route delivery robots by providing
                    price assessments and pricing comparisons
                  </div>
                  <div>2. Any other task you'd like assistance with</div>
                </div>
                <div className="text-[#0d0d0d]">
                  Please let me know how you'd like to proceed, and I'll be happy to help.
                </div>
              </div>

              <Button
                variant="outline"
                size="sm"
                className="w-fit border-[#e2e2e2] text-[#0d0d0d] hover:bg-[#ffffff] bg-transparent"
              >
                First option
              </Button>

              <div className="space-y-3">
                <div className="text-[#0d0d0d]">
                  I am searching the selected online sources on the customised route delivery robots.
                </div>

                <div className="text-[#6b5be9] text-xs">Searching...</div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-xs text-[#a3a3a3]">
                    <Search className="w-3 h-3" />
                    Alibaba customised delivery robots
                  </div>
                  <div className="flex items-center gap-2 text-xs text-[#a3a3a3]">
                    <Search className="w-3 h-3" />
                    AliExpress customised delivery robots
                  </div>
                </div>

                <div className="text-[#0d0d0d]">
                  I am finalising the collection of information because I have gathered enough relevant sources covering
                  various important topics, ensuring a comprehensive understanding of the subject.
                </div>

                <div className="text-[#0d0d0d]">I am writing a final research report.</div>

                <div className="text-[#6b5be9] text-xs">PriceAlly is working...</div>
              </div>
            </div>
          </ScrollArea>

          {/* Chat Input */}
          <div className="p-4 border-t border-[#e2e2e2]">
            <div className="flex items-center gap-2 mb-3">
              <Button variant="ghost" size="sm" className="text-xs text-[#a3a3a3] hover:bg-[#ffffff]">
                GPT-4.1
                <ChevronDown className="w-3 h-3 ml-1" />
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex-1 relative">
                <Input
                  placeholder="Ask me anything"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="pr-20 border-[#e2e2e2] bg-[#ffffff] text-[#0d0d0d] placeholder:text-[#a3a3a3]"
                />
                <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Paperclip className="w-3 h-3" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Globe className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              <Button size="sm" className="bg-[#0d0d0d] text-[#ffffff] hover:bg-[#000000]">
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Content Header */}
          <div className="flex items-center justify-between p-6 border-b border-[#e2e2e2]">
            <h1 className="text-lg font-semibold text-[#0d0d0d]">Development of a Customised Route Delivery Robot</h1>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" className="border border-[#e2e2e2] hover:bg-[#fafafa]">
                <Undo2 className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="border border-[#e2e2e2] hover:bg-[#fafafa]">
                <Redo2 className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Content Body */}
          <ScrollArea className="flex-1 p-6">
            <div className="max-w-4xl space-y-6">
              {/* System Overview */}
              <section>
                <h2 className="text-lg font-semibold text-[#0d0d0d] mb-3">1. System Overview</h2>
                <p className="text-[#0d0d0d] leading-relaxed">
                  This document outlines the design for a customized route delivery robot for use within an office
                  environment. The robot will be fully autonomous, utilizing VSLAM for navigation, and controllable via
                  a mobile app or remote control. It will prioritize safety with obstacle avoidance features and operate
                  for 24 hours on a single charge with auto-recharge functionality. A 10-inch screen will display robot
                  status and allow for function selection. The robot's footprint will not exceed 38 x 38 x 103 cm for
                  easy maneuverability. A 1-year warranty and after-warranty service, including video technical support,
                  will be included.
                </p>
              </section>

              {/* Key Components */}
              <section>
                <h2 className="text-lg font-semibold text-[#0d0d0d] mb-4">2. Key Components</h2>
                <div className="space-y-4">
                  <div>
                    <span className="font-medium text-[#0d0d0d]">• Chassis:</span>
                    <span className="text-[#0d0d0d] ml-1">
                      The robot's base will be constructed from lightweight yet durable aluminum, providing a sturdy
                      platform for the other components.
                    </span>
                  </div>

                  <div>
                    <span className="font-medium text-[#0d0d0d]">• Drive System:</span>
                    <span className="text-[#0d0d0d] ml-1">
                      Two high-torque DC motors will power the robot's wheels, enabling efficient movement and precise
                      maneuvering within the office environment.
                    </span>
                  </div>

                  <div>
                    <span className="font-medium text-[#0d0d0d]">• Navigation System:</span>
                    <div className="ml-4 mt-2 space-y-2">
                      <div>
                        <span className="font-medium text-[#0d0d0d]">• VSLAM:</span>
                        <span className="text-[#0d0d0d] ml-1">
                          The robot will utilize VSLAM technology for autonomous navigation. A camera mounted on the
                          robot will capture images of the environment, which will be processed by the onboard computer
                          to create a real-time map. This map will be used to plan and execute the robot's route.
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-[#0d0d0d]">• LIDAR:</span>
                        <span className="text-[#0d0d0d] ml-1">
                          A LIDAR sensor will provide additional data about the surrounding environment, enhancing the
                          robot's ability to detect and avoid obstacles.
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <span className="font-medium text-[#0d0d0d]">• Control System</span>
                    <div className="ml-4 mt-2 space-y-2">
                      <div>
                        <span className="font-medium text-[#0d0d0d]">• Programmable Automation Controller (PAC):</span>
                        <span className="text-[#0d0d0d] ml-1">
                          A PAC will serve as the robot's central processing unit, controlling all aspects of its
                          operation, including motor control, sensor data processing, and communication with the mobile
                          app.
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-[#0d0d0d]">• Mobile App/Remote Control:</span>
                        <span className="text-[#0d0d0d] ml-1">
                          Users will be able to control the robot's movement and functionality through a dedicated
                          mobile app or a physical remote control.
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <span className="font-medium text-[#0d0d0d]">• Safety System:</span>
                    <div className="ml-4 mt-2 space-y-2">
                      <div>
                        <span className="font-medium text-[#0d0d0d]">• Obstacle Avoidance:</span>
                        <span className="text-[#0d0d0d] ml-1">
                          The robot will be equipped with multiple ultrasonic sensors and a LIDAR sensor to detect
                          obstacles in its path. The PAC will process this data and adjust the robot's trajectory to
                          avoid collisions.
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-[#0d0d0d]">• Emergency Stop Button:</span>
                        <span className="text-[#0d0d0d] ml-1">
                          A prominent emergency stop button will be easily accessible to users, allowing them to
                          immediately halt the robot's movement in case of an emergency.
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <span className="font-medium text-[#0d0d0d]">• Power System:</span>
                    <div className="ml-4 mt-2 space-y-2">
                      <div>
                        <span className="font-medium text-[#0d0d0d]">• Battery:</span>
                        <span className="text-[#0d0d0d] ml-1">
                          A high-capacity lithium-ion battery will provide the robot with 24 hours of operation on a
                          single 8-hour charge.
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-[#0d0d0d]">• Auto-Recharge Function:</span>
                        <span className="text-[#0d0d0d] ml-1">
                          The robot will automatically return to its charging station during office hours to recharge
                          its battery. This ensures continued operation beyond office hours.
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <div className="pt-8 text-center text-xs text-[#a3a3a3]">Powered by HTX.</div>
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  )
}
